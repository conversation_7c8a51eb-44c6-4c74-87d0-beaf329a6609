<?php
$is_defend = true;
$nosession = true;
require './includes/common.php';

@header('Content-Type: text/html; charset=UTF-8');

$other = isset($_GET['other']) ? true : false;
$trade_no = daddslashes($_GET['trade_no']);
$sitename = base64_decode(daddslashes($_GET['sitename']));
$row = $DB->getRow("SELECT * FROM pre_order WHERE trade_no='{$trade_no}' limit 1");
if (!$row) sysmsg('该订单号不存在，请返回来源地重新发起请求！');
if ($row['status'] == 1) sysmsg('该订单已完成支付，请勿重复支付');
$gid = $DB->getColumn("SELECT gid FROM pre_user WHERE uid='{$row['uid']}' limit 1");
$paytype = \lib\Channel::getTypes($row['uid'], $gid);

if (strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false) {
    $paytype = array_values($paytype);
    foreach ($paytype as $i => $s) {
        if ($s['name'] == 'wxpay') {
            $temp = $paytype[$i];
            $paytype[$i] = $paytype[0];
            $paytype[0] = $temp;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=0">
<title>收银台 | <?php echo $sitename ? $sitename : $conf['sitename'] ?> </title>
<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
<style>
    body {
        font-family: 'Roboto', sans-serif;
        background: #f2f2f2;
        margin: 0;
        padding: 0;
        color: #333;
    }
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    .nav {
        background: #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 20px;
    }
    .nav img {
        height: 50px;
    }
    .nav-right {
        font-size: 18px;
        font-weight: bold;
    }
    .order-info, .payment-method, .payment-action, .footer {
        background: #fff;
        margin-bottom: 20px;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .order-info ul {
        list-style: none;
        padding: 0;
    }
    .order-info ul li {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    .order-info ul li span:first-child {
        font-weight: bold;
    }
    .payment-method h2, .payment-action span {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 20px;
    }
    .payment-method ul {
        display: flex;
        list-style: none;
        padding: 0;
    }
    .payment-method ul li {
        flex: 1;
        text-align: center;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-right: 10px;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .payment-method ul li:last-child {
        margin-right: 0;
    }
    .payment-method ul li.active,
    .payment-method ul li:hover {
        border-color: #007BFF;
        background: #007BFF;
        color: #fff;
    }
    .payment-method ul li img {
        max-height: 40px;
        margin-bottom: 10px;
        width: auto;
    }
    .payment-action {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .payment-action strong {
        color: #007BFF;
        font-size: 24px;
        margin-left: 10px;
    }
    .payment-action a {
        background: #007BFF;
        color: #fff;
        padding: 10px 20px;
        border-radius: 8px;
        text-decoration: none;
        transition: all 0.3s;
    }
    .payment-action a:hover {
        background: #0056b3;
    }
    .footer {
        text-align: center;
        font-size: 14px;
        color: #888;
    }
</style>
</head>
<body>
<div class="nav">
    <div class="nav-left">
    </div>
    <div class="nav-right">
        收银台
    </div>
</div>
<div class="container">
<input type="hidden" name="trade_no" value="<?php echo $trade_no ?>"/>
<!--订单金额-->
<?php if ($other) { ?>
<div class="order-info">
    <h2 style="color: red">当前支付方式暂时关闭维护，请更换其他方式支付</h2>
    <h3 style="color: green">如果您需要微信支付请将微信余额转到QQ再选择QQ钱包支付！</h3>
    <h3><a href="./wx.html" style="color: blue;">点击查看微信余额转到QQ钱包教程</a></h3>
</div>
<?php } else { ?>
<div class="order-info">
    <ul>
        <li>
            <span>订单号：</span>
            <span><?php echo $trade_no ?></span>
        </li>
        <li>
            <span>创建时间：</span>
            <span><?php echo $row['addtime'] ?></span>
        </li>
    </ul>
    <div class="order-amount">
        <span>订单金额：</span>
        <strong><?php echo $row['money'] ?></strong>
        <span>元</span>
    </div>
</div>
<?php } ?>
<!--支付方式-->
<div class="payment-method">
    <h2>请选择支付方式</h2>
    <ul class="types">
        <?php foreach ($paytype as $rows) { ?>
          <li class="pay_li" value="<?php echo $rows['id'] ?>">
             <img src="/assets/icon/<?php echo $rows['name'] ?>.ico" alt="<?php echo $rows['showname'] ?>">
             <span><?php echo $rows['showname'] ?></span>
          </li>
        <?php } ?>
    </ul>
</div>
<!--立即支付-->
<div class="payment-action">
    <a href="javascript:void(0)" class="immediate_pay">立即支付</a>
</div>
</div>

<script src="<?php echo $cdnpublic ?>jquery/1.12.4/jquery.min.js"></script>
<script type="text/javascript">
$(document).ready(function(){
    $(".types li").click(function(){
        $(".types li").removeClass('active');
        $(this).addClass('active');
    });
    
    $(document).on("click", ".immediate_pay", function() {
        var value = $(".types").find('.active').attr('value');
        var trade_no = $("input[name='trade_no']").val();
        window.location.href = './submit2.php?typeid=' + value + '&trade_no=' + trade_no;
    });
    
    $(".types li:first").click();
});
</script>
</body>
</html>